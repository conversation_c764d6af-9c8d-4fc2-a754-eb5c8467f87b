{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "D:/Software/Cmake/bin/cmake.exe", "cpack": "D:/Software/Cmake/bin/cpack.exe", "ctest": "D:/Software/Cmake/bin/ctest.exe", "root": "D:/Software/Cmake/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 1, "string": "3.30.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-90b6c3f7c5ba1815474c.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-2a35c15ff7e772d40373.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-df7e76267b1ea06b042f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-159270f7b5df9bf6a6bb.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-2a35c15ff7e772d40373.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-90b6c3f7c5ba1815474c.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-159270f7b5df9bf6a6bb.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-df7e76267b1ea06b042f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}